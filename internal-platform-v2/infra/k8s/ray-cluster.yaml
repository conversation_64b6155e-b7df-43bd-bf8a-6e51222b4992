# SPDX-FileCopyrightText: Terrafloww Labs, 2025

apiVersion: ray.io/v1alpha1
kind: RayCluster
metadata:
  name: terrafloww-ray-cluster
  labels:
    app: terrafloww
    component: ray-cluster
spec:
  # Ray version must match the version in the custom Docker image
  rayVersion: '2.47.1'
  
  # Enable autoscaling for dynamic worker scaling
  enableInTreeAutoscaling: true
  
  # Head node configuration
  headGroupSpec:
    serviceType: ClusterIP
    rayStartParams:
      dashboard-host: '0.0.0.0'
      dashboard-port: '8265'
      object-store-memory: '536870912'  # 0.5GB object store (reduced from 1GB)
      num-cpus: '0'  # Head node doesn't run workloads
      block: 'true'
    template:
      metadata:
        labels:
          app: terrafloww
          component: ray-head
      spec:
        # Schedule Ray head on data pool nodes
        nodeSelector:
          doks.digitalocean.com/node-pool: data-pool-e0y1e6npd
        tolerations:
        - key: workload
          operator: Equal
          value: data
          effect: NoSchedule
        imagePullSecrets:
        - name: registry-terrafloww-dev
        containers:
        - name: ray-head
          image: registry.digitalocean.com/terrafloww-dev/ray-custom:2.47.1
          imagePullPolicy: Always
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "ray stop"]
          ports:
          - containerPort: 6379  # GCS server
            name: gcs-server
          - containerPort: 8265  # Dashboard
            name: dashboard
          - containerPort: 10001  # Ray client
            name: client
          resources:
            limits:
              cpu: "2"
              memory: "6Gi"  # Increased for dashboard and metrics
            requests:
              cpu: "1"
              memory: "4Gi"  # Increased from 1Gi
          readinessProbe:
            exec:
              command:
              - bash
              - -c
              - wget -T 4 -q -O- http://localhost:52365/api/local_raylet_healthz | grep success &&
                wget -T 10 -q -O- http://localhost:8265/api/gcs_healthz | grep success
            periodSeconds: 5
            timeoutSeconds: 10
            failureThreshold: 12  # 60s grace period
          env:
          - name: RAY_DISABLE_IMPORT_WARNING
            value: "1"
          - name: RAY_DEDUP_LOGS
            value: "0"
          - name: RAY_DASHBOARD_METRICS_COLLECTION_ENABLED
            value: "0"  # Disable metrics to reduce memory usage
          - name: RAY_USAGE_STATS_ENABLED
            value: "0"  # Save ~100MB and periodic HTTP calls
          - name: FLIGHT_HOST
            value: "terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local"
          - name: FLIGHT_PORT
            value: "50052"
          volumeMounts:
          - mountPath: /tmp/ray
            name: ray-logs
          - mountPath: /dev/shm
            name: shared-mem
        volumes:
        - name: ray-logs
          emptyDir: {}
        - name: shared-mem
          emptyDir:
            medium: Memory
            sizeLimit: 4Gi

  # Worker group configuration with autoscaling
  workerGroupSpecs:
  - replicas: 1
    minReplicas: 1
    maxReplicas: 5
    groupName: worker-group
    rayStartParams:
      num-cpus: '2'
      block: 'true'
    template:
      metadata:
        labels:
          app: terrafloww
          component: ray-worker
      spec:
        # Schedule Ray workers on data pool nodes
        nodeSelector:
          doks.digitalocean.com/node-pool: data-pool-e0y1e6npd
        tolerations:
        - key: workload
          operator: Equal
          value: data
          effect: NoSchedule
        imagePullSecrets:
        - name: registry-terrafloww-dev
        containers:
        - name: ray-worker
          image: registry.digitalocean.com/terrafloww-dev/ray-custom:2.47.1
          imagePullPolicy: Always
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "ray stop"]
          resources:
            limits:
              cpu: "2"
              memory: "4Gi"  # Increased for better performance
            requests:
              cpu: "1"
              memory: "2Gi"  # Increased from 1Gi
          env:
          - name: RAY_DISABLE_IMPORT_WARNING
            value: "1"
          - name: RAY_DEDUP_LOGS
            value: "0"
          - name: RAY_USAGE_STATS_ENABLED
            value: "0"  # Save memory and HTTP calls
          - name: FLIGHT_HOST
            value: "terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local"
          - name: FLIGHT_PORT
            value: "50052"
          volumeMounts:
          - mountPath: /tmp/ray
            name: ray-logs
          - mountPath: /dev/shm
            name: shared-mem
        volumes:
        - name: ray-logs
          emptyDir: {}
        - name: shared-mem
          emptyDir:
            medium: Memory
            sizeLimit: 2Gi
        
        # Restart policy for worker pods
        restartPolicy: Never
