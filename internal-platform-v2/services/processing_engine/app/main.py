# services/processing_engine/app/main.py

import os, sys
import logging
import asyncio # Import asyncio

# Use grpc.aio for async server
import grpc.aio
import pyarrow.flight as flight

# Import processing engine protos
from terrafloww.processing_engine.v1 import processing_engine_pb2
from terrafloww.processing_engine.v1 import processing_engine_pb2_grpc

# Import service implementations
from .flight_server import FlightServer
from .grpc_service import ProcessingEngineServicer, add_ProcessingEngineService_to_server

# Configure logging (no change needed)
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('processing_engine.log', mode='a')
    ]
)

# Set specific loggers to DEBUG
for logger_name in [
    'terrafloww.engine_core',
    'services.processing_engine',
    'grpc',
    'pyarrow',
    'terrafloww.sdk.tesseract',
    'terrafloww.engine_core.runtime_ray',
    'terrafloww.engine_core.process',
    'terrafloww.engine_core.runtime_ray.worker',
    'terrafloww.engine_core.runtime_ray.driver',
    'terrafloww.engine_core.runtime_ray.planner'
]:
    logging.getLogger(logger_name).setLevel(logging.DEBUG)

logger = logging.getLogger(__name__)


async def serve():
    """Starts BOTH gRPC and Flight servers concurrently."""

    # --- Configure gRPC Server (using grpc.aio) ---
    grpc_port = os.environ.get("GRPC_PORT", "50051")
    grpc_server = grpc.aio.server() # Create async gRPC server
    grpc_server.add_insecure_port(f"[::]:{grpc_port}")

    # Instantiate Flight Server first (needed for gRPC servicer if reference is passed)
    flight_port = os.environ.get("FLIGHT_PORT", "50052")
    # Correct Flight location format: grpc+tcp:// or grpc+tls:// [3]
    flight_location = f"grpc+tcp://0.0.0.0:{flight_port}" # Example insecure location
    # Pass location to FlightServer constructor [6]
    flight_server_instance = FlightServer(location=flight_location)

    # Instantiate gRPC Servicer (can pass flight instance if needed)
    grpc_servicer_instance = ProcessingEngineServicer(flight_service=None) # Not passing flight ref for now

    # Add servicer to gRPC server
    add_ProcessingEngineService_to_server(grpc_servicer_instance, grpc_server)

    logger.info(f"Starting gRPC server on port {grpc_port}...")
    await grpc_server.start()
    logger.info("gRPC server started.")

    # --- Configure and Start Flight Server ---
    # The FlightServer needs to run within the same event loop.
    # FlightServer.serve() is blocking, so we need to run it in a way
    # that doesn't block the main asyncio task.
    # However, FlightServerBase itself doesn't seem to directly expose an async serve method.
    # Common pattern: Start Flight server and let it run, potentially using its internal event loop integration if available,
    # or wrap the blocking serve call if necessary (though less ideal).
    # Let's try starting it directly and see if its base implementation handles the asyncio loop.
    # If flight_server_instance.serve() blocks indefinitely, we might need to wrap it
    # with loop.run_in_executor or look for alternative async startup if pyarrow offers it.
    # For now, assume it integrates or blocks cleanly in a way asyncio handles.
    # It's often simpler to run Flight in a separate thread/process if async integration is tricky.

    # --> Let's simplify: Run the blocking serve() call after gRPC start and wait on both.
    # This means Flight server startup *might* block gRPC serving if `serve()` is purely blocking
    # and doesn't yield to the asyncio loop. Let's test this first.

    logger.info(f"Starting Flight server at {flight_location}...")
    # flight_server_instance.serve() # This is blocking, run separately below

    # --- Run Servers and Wait ---
    # Keep servers running until interrupted
    # We can await both server's wait_for_termination methods.
    # FlightServer doesn't have an explicit async wait_for_termination,
    # but serve() blocks. We can await the gRPC server's termination.

    # Start flight server (blocking call) - Need to run concurrently
    # We can create a task for the gRPC server's wait logic
    grpc_termination_task = asyncio.create_task(grpc_server.wait_for_termination())

    # How to run Flight? Let's try running serve() and see if it yields control.
    # If not, we might need threading or run_in_executor.
    # Alternative: Run Flight server first? No, gRPC needs to start too.

    # --> Revised concurrent approach: Start gRPC, then start Flight's serve in loop.
    try:
        # Run Flight server (this blocks, but hopefully yields to asyncio loop)
        # This is the simplest attempt, might need revision based on FlightServer behavior
        # flight_server_instance.serve(flight_location) # This blocks, so we can't await grpc termination easily after this.

        # --> Better approach: Run the BLOCKING flight server serve() in an executor thread [5]
        loop = asyncio.get_running_loop()
        flight_serve_task = loop.run_in_executor(
            None, # Use default ThreadPoolExecutor
            flight_server_instance.serve # Pass the serve method
            # Arguments for serve if needed, but location is set in init now
        )
        logger.info("Flight server started in background thread.")

        # Wait for either server to terminate (e.g., on SIGTERM)
        await grpc_server.wait_for_termination() # Wait for gRPC termination signal

        # Clean up flight task if gRPC stops (optional)
        # flight_serve_task.cancel() # Requires more graceful shutdown logic

    except KeyboardInterrupt:
        logger.info("Received interrupt signal.")
    finally:
        logger.info("Stopping gRPC server...")
        await grpc_server.stop(grace=1.0) # Graceful shutdown [2]
        logger.info("gRPC server stopped.")
        # Add Flight server shutdown if possible/needed
        # flight_server_instance.shutdown() # Check pyarrow docs for shutdown method
        logger.info("Servers shut down.")


if __name__ == "__main__":
    # Remove TFW_SERVER_TYPE logic, always run both
    logger.info("Starting Processing Engine (gRPC + Flight)...")
    try:
        asyncio.run(serve())
    except Exception as e:
         logger.critical(f"Failed to run servers: {e}", exc_info=True)