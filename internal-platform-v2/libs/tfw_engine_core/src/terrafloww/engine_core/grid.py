"""
Grid utilities for COG data processing.
"""
import logging, sys
from typing import List, Tuple, Dict, Any, Optional
from shapely.geometry import Polygon, box
from rasterio.windows import Window
from rasterio.transform import Affine
import numpy as np
from shapely.geometry.base import BaseGeometry

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", stream=sys.stdout
)
logger = logging.getLogger(__name__)

def _get_intersecting_tile_indices_by_bounds(
    geometry: Polygon,
    grid_transform: Affine,  # Expect Affine object
    image_width: int,
    image_height: int,
    tile_width: int,
    tile_height: int,
    debug: bool = False,
) -> List[Tuple[int, int]]:
    """
    Find all tile indices (row, col) that intersect with the given geometry.
    
    Args:
        geometry: Shapely Polygon in the same CRS as the grid
        grid_transform: Affine transform for the entire grid
        image_width: Width of the entire image in pixels
        image_height: Height of the entire image in pixels
        tile_width: Width of each tile in pixels
        tile_height: Height of each tile in pixels
        debug: Whether to log debug information
        
    Returns:
        List of (row, col) tuples for intersecting tiles
    """
    # Calculate the number of tiles in each dimension
    num_tiles_x = (image_width + tile_width - 1) // tile_width
    num_tiles_y = (image_height + tile_height - 1) // tile_height
    
    if debug:
        logger.debug(
            "\n\n***Entering _get_intersecting_tile_indices_by_bounds***\n"
        )  # Entry log

    tiles_x = (image_width + tile_width - 1) // tile_width
    tiles_y = (image_height + tile_height - 1) // tile_height
    minx, miny, maxx, maxy = geometry.bounds

    try:
        inv_transform = ~grid_transform
        # --- Corrected Transformation: Iterate through points ---
        xs_crs = [minx, maxx, maxx, minx]
        ys_crs = [miny, miny, maxy, maxy]
        transformed_coords = [inv_transform * pt for pt in zip(xs_crs, ys_crs)]
        cols_f = [c[0] for c in transformed_coords]
        rows_f = [c[1] for c in transformed_coords]

        # Get the range of tiles that might intersect
        min_col = max(0, int(np.floor(min(cols_f) / tile_width)))
        max_col = min(tiles_x - 1, int(np.ceil(max(cols_f) / tile_width)))
        min_row = max(0, int(np.floor(min(rows_f) / tile_height)))
        max_row = min(tiles_y - 1, int(np.ceil(max(rows_f) / tile_height)))

        if debug:
            logger.debug(
                f"Transformed coords: {transformed_coords}\n"
                f"Cols range: {min_col}-{max_col}, Rows range: {min_row}-{max_row}"
            )

        # Check each candidate tile for intersection
        intersecting_tiles = []
        for r in range(min_row, max_row + 1):
            for c in range(min_col, max_col + 1):
                # Calculate tile bounds in pixel space
                tile_x_min = c * tile_width
                tile_y_min = r * tile_height
                tile_x_max = min((c + 1) * tile_width, image_width)
                tile_y_max = min((r + 1) * tile_height, image_height)

                # Convert to world coordinates
                world_coords = [
                    grid_transform * (tile_x_min, tile_y_min),  # Top-left
                    grid_transform * (tile_x_max, tile_y_min),  # Top-right
                    grid_transform * (tile_x_max, tile_y_max),  # Bottom-right
                    grid_transform * (tile_x_min, tile_y_max),  # Bottom-left
                ]

                # Create a polygon for the tile
                xs = [pt[0] for pt in world_coords]
                ys = [pt[1] for pt in world_coords]
                tile_poly = Polygon(zip(xs, ys))

                # Check intersection
                if tile_poly.intersects(geometry):
                    intersecting_tiles.append((r, c))
                    if debug:
                        logger.debug(f"Tile ({r}, {c}) intersects with geometry")

        if debug:
            logger.debug(
                f"Found {len(intersecting_tiles)} intersecting tiles: {intersecting_tiles}"
            )
        return intersecting_tiles

    except Exception as e:
        logger.error(f"Error calculating intersecting tiles: {e}", exc_info=True)
        return []  # Return empty list on error


def _calculate_intersection_window(
    geometry: Polygon,
    tile_transform: Affine,  # Specific transform for *this* tile
    tile_width: int,
    tile_height: int,
    debug: bool = False,
) -> Window:
    """
    Calculates the precise pixel window of intersection for a single tile.
    Internal helper function.
    """
    if debug:
        logger.debug("\n\n***Entering _calculate_intersection_window***\n")

    try:
        # Get the inverse transform to convert world coords to pixel coords
        inv_transform = ~tile_transform

        # Get the bounds of the geometry
        minx, miny, maxx, maxy = geometry.bounds

        # Convert to pixel coordinates
        ul_px, ul_py = inv_transform * (minx, miny)  # Upper left
        lr_px, lr_py = inv_transform * (maxx, maxy)  # Lower right

        # Ensure correct order (pixel space may be flipped)
        min_px, max_px = sorted([ul_px, lr_px])
        min_py, max_py = sorted([ul_py, lr_py])

        # Clamp to tile boundaries and round outward
        col_off = max(0, int(np.floor(min_px)))
        row_off = max(0, int(np.floor(min_py)))
        col_end = min(tile_width, int(np.ceil(max_px)))
        row_end = min(tile_height, int(np.ceil(max_py)))

        # Calculate width and height
        width = col_end - col_off
        height = row_end - row_off

        if debug:
            logger.debug(
                f"Geometry bounds (world): {minx}, {miny}, {maxx}, {maxy}\n"
                f"Pixel coords: UL=({ul_px}, {ul_py}), LR=({lr_px}, {lr_py})\n"
                f"Window: col_off={col_off}, row_off={row_off}, width={width}, height={height}"
            )

        # Create and return the window
        return Window(col_off=col_off, row_off=row_off, width=width, height=height)

    except Exception as e:
        logger.error(f"Error calculating intersection window: {e}", exc_info=True)
        # Return a minimal valid window on error
        return Window(col_off=0, row_off=0, width=1, height=1)


def get_intersecting_tiles_and_windows(
    grid_definition: Dict[str, Any], # Grid definition comes first now
    aoi: Optional[BaseGeometry] = None, # AOI is now optional
    spatial_subdivision_factor: int = 1, # NEW: Split each tile into NxN sub-windows for better parallelism
    debug: bool = False
) -> List[Dict[str, Any]]:
    """
    Compute tile indices (row, col) and the precise pixel window within each
    tile that intersects with the given AOI, or all tiles if AOI is None.
    Optionally subdivides each tile into smaller sub-windows for better parallelism.

    Args:
        grid_definition: Dictionary with grid parameters:
            - transform: Affine transform for the entire grid
            - width: Width of the entire image in pixels
            - height: Height of the entire image in pixels
            - tile_width: Width of each tile in pixels
            - tile_height: Height of each tile in pixels
        aoi: Optional Shapely geometry in the same CRS as the grid
        spatial_subdivision_factor: Number of sub-windows per tile dimension (default: 1)
            - 1: No subdivision (original behavior)
            - 2: Split each tile into 2x2 = 4 sub-windows
            - 3: Split each tile into 3x3 = 9 sub-windows
        debug: Whether to log debug information

    Returns:
        List of dictionaries, each containing:
            - tile_r: Row index of the tile
            - tile_c: Column index of the tile
            - window: Window object for the intersection
            - sub_r: Sub-window row index (0 if no subdivision)
            - sub_c: Sub-window column index (0 if no subdivision)
    """
    if debug:
        logger.debug("\n\n***Entering get_intersecting_tiles_and_windows***\n")
        
    # Extract grid parameters
    transform = grid_definition["transform"]
    width = grid_definition["width"]
    height = grid_definition["height"]
    tile_width = grid_definition["tile_width"]
    tile_height = grid_definition["tile_height"]
    
    # Validate transform type
    if not isinstance(transform, Affine):
        if isinstance(transform, (list, tuple)) and len(transform) == 6:
            transform = Affine(*transform)
        else:
            raise ValueError(f"Invalid transform type: {type(transform)}. Expected Affine or 6-element tuple/list.")
    
    # If no AOI is provided, use the entire grid
    if aoi is None:
        if debug:
            logger.debug("No AOI provided, using entire grid")
            
        # Calculate the number of tiles in each dimension
        num_tiles_x = (width + tile_width - 1) // tile_width
        num_tiles_y = (height + tile_height - 1) // tile_height
        
        result = []
        for tile_r in range(num_tiles_y):
            for tile_c in range(num_tiles_x):
                # Calculate actual tile dimensions (handle edge tiles)
                actual_width = min(tile_width, width - tile_c * tile_width)
                actual_height = min(tile_height, height - tile_r * tile_height)
                
                # Create a window for the entire tile
                window = Window(0, 0, actual_width, actual_height)
                
                result.append({
                    "tile_r": tile_r,
                    "tile_c": tile_c,
                    "window": window,
                })
                
        if debug:
            logger.debug(f"Generated {len(result)} tiles for the entire grid")
            
        return result
    
    # Ensure AOI is a Polygon
    if not isinstance(aoi, Polygon):
        try:
            aoi = Polygon(aoi)
        except Exception as e:
            logger.error(f"Failed to convert AOI to Polygon: {e}", exc_info=True)
            raise ValueError(f"AOI must be a Polygon or convertible to one: {e}")
    
    # Get all tile indices that intersect with the AOI
    intersecting_tiles = _get_intersecting_tile_indices_by_bounds(
        aoi, transform, width, height, tile_width, tile_height, debug
    )
    
    if debug:
        logger.debug(f"Found {len(intersecting_tiles)} intersecting tiles")
    
    result = []
    for tile_r, tile_c in intersecting_tiles:
        # Calculate the transform for this specific tile
        # Shift the origin to the top-left of the tile
        tile_transform = Affine(
            transform.a, transform.b, transform.c + tile_c * tile_width * transform.a,
            transform.d, transform.e, transform.f + tile_r * tile_height * transform.e,
        )
        
        # Calculate the window of intersection
        window = _calculate_intersection_window(
            aoi, tile_transform, tile_width, tile_height, debug
        )
        
        # Skip tiles with empty windows
        if window.width <= 0 or window.height <= 0:
            if debug:
                logger.debug(f"Skipping tile ({tile_r}, {tile_c}) with empty window")
            continue
            
        result.append({
            "tile_r": tile_r,
            "tile_c": tile_c,
            "window": window,
        })
    
    if debug:
        logger.debug(f"Returning {len(result)} tiles with valid windows")
        
    return result


def calculate_window_bounds(
    tile_r: int,
    tile_c: int,
    window: Window, # Window relative to the tile's top-left
    grid_definition: Dict[str, Any]
) -> List[float]:
    """
    Calculates the geographic bounds [minx, miny, maxx, maxy] for a specific
    pixel window within a specific tile of the grid.
    
    Args:
        tile_r: Row index of the tile
        tile_c: Column index of the tile
        window: Window object relative to the tile's top-left
        grid_definition: Dictionary with grid parameters
        
    Returns:
        List [minx, miny, maxx, maxy] representing the bounds
    """
    # Extract grid parameters
    transform = grid_definition["transform"]
    tile_width = grid_definition.get("tile_width", 256)  # Default values if not provided
    tile_height = grid_definition.get("tile_height", 256)
    
    # Validate transform type
    if not isinstance(transform, Affine):
        if isinstance(transform, (list, tuple)) and len(transform) == 6:
            transform = Affine(*transform)
        else:
            raise ValueError(f"Invalid transform type: {type(transform)}. Expected Affine or 6-element tuple/list.")
    
    # Calculate the transform for this specific tile
    # Shift the origin to the top-left of the tile
    tile_transform = Affine(
        transform.a, transform.b, transform.c + tile_c * tile_width * transform.a,
        transform.d, transform.e, transform.f + tile_r * tile_height * transform.e,
    )
    
    # Calculate the world coordinates of the window corners
    # Top-left corner
    x_min, y_min = tile_transform * (window.col_off, window.row_off)
    
    # Bottom-right corner
    x_max, y_max = tile_transform * (window.col_off + window.width, window.row_off + window.height)
    
    # Ensure the bounds are in the correct order
    minx = min(x_min, x_max)
    miny = min(y_min, y_max)
    maxx = max(x_min, x_max)
    maxy = max(y_min, y_max)
    
    return [minx, miny, maxx, maxy]
