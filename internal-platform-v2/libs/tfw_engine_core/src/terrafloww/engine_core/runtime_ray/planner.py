# libs/tfw_engine_core/src/terrafloww/engine_core/runtime_ray/planner.py

import logging
import os, sys
from typing import List, Optional, Tuple, Dict, Any, AsyncIterator # Changed output type
import asyncio # Needed for async generator

import pyarrow as pa
import pyarrow.compute as pc # Import compute
from shapely.wkt import loads as wkt_loads
from shapely.geometry.base import BaseGeometry
import pytz # For timezone handling
from rasterio.transform import Affine # Import Affine

# Import protobuf definitions
from terrafloww.processing_engine.v1 import processing_engine_pb2

# Import core engine components
from terrafloww.engine_core.catalog_client import (
    CatalogClient,
    S3Config,
    S3StorageBackend,
    LocalStorageBackend,
    get_band_aliases
)
from terrafloww.engine_core import grid
from terrafloww.engine_core import utils as engine_utils
# Import the WindowSpec dataclass
from .common_types import WindowSpec

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", stream=sys.stdout
)
logger = logging.getLogger(__name__)

# Required fields from catalog (keep as before)
REQUIRED_CATALOG_FIELDS = [
    "scene_id", "collection", "datetime", "cog_key", "cog_href",
    "cog_width", "cog_height", "cog_tile_width", "cog_tile_height",
    "cog_crs", "cog_dtype", "cog_predictor", "cog_tile_offsets",
    "cog_tile_byte_counts", "cog_scale", "cog_offset", "cog_transform"
]


# No longer need _limit_window_specs_by_tile_group here, driver handles limit

def _create_catalog_client() -> CatalogClient:
    """
    Create catalog client with cloud-native configuration support.

    Supports both legacy environment variable configuration and new cloud-native setup.
    """
    # Check for cloud-native S3 configuration first
    s3_bucket = os.environ.get("STAC_CATALOG_S3_BUCKET")
    s3_endpoint = os.environ.get("STAC_CATALOG_S3_ENDPOINT") or os.environ.get("DO_SPACE_ENDPOINT")
    s3_region = os.environ.get("STAC_CATALOG_S3_REGION") or os.environ.get("DO_REGION", "nyc3")
    s3_path_prefix = os.environ.get("STAC_CATALOG_S3_PATH_PREFIX", "")

    if s3_bucket and s3_endpoint:
        # Use S3/cloud storage backend
        logger.info(f"Configuring S3 catalog backend: bucket={s3_bucket}, endpoint={s3_endpoint}")

        s3_config = S3Config(
            bucket=s3_bucket,
            region=s3_region,
            endpoint_url=s3_endpoint,
            path_prefix=s3_path_prefix
        )

        storage_backend = S3StorageBackend(s3_config)
        return CatalogClient(storage_backend=storage_backend)

    else:
        # Fall back to legacy configuration
        legacy_path = os.environ.get("TFW_EXTERNAL_CATALOG_PATH", "/tmp/platform_delta_tables/ext_stac_datasets")
        logger.info(f"Using legacy catalog configuration: {legacy_path}")

        # The CatalogClient constructor will handle S3 vs local path detection
        return CatalogClient(catalog_path=legacy_path)

async def plan_execution(plan: processing_engine_pb2.WorkflowPlan) -> AsyncIterator[WindowSpec]:
    """
    Takes a logical WorkflowPlan and yields WindowSpec objects defining
    the physical work units for Ray workers.

    Args:
        plan: The WorkflowPlan protobuf message.

    Yields:
        WindowSpec objects defining individual work units (band-windows).
    """
    logger.info("Starting execution planning (async generator)...")

    # --- 1. Instantiate Catalog Client ---
    try:
        catalog_client = _create_catalog_client()
        logger.info(f"CatalogClient initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize CatalogClient: {e}", exc_info=True)
        return # Use return in async generator to signal termination

    # --- 2. Parse Load Parameters & Query Catalog ---
    load_params = plan.load_step
    logger.info(f"Planning generator for collection: {load_params.collection}, Bands: {list(load_params.bands)}")

    # Parse AOI (same logic as before)
    aoi_geom: Optional[BaseGeometry] = None
    aoi_src_crs : Optional[str] = None # Keep track of original AOI CRS
    if load_params.aoi_wkt:
        try:
            aoi_geom = wkt_loads(load_params.aoi_wkt)
            aoi_src_crs = load_params.aoi_crs or "EPSG:4326" # Default if not provided
            logger.info(f"Parsed AOI: {aoi_geom.geom_type} (Bounds: {aoi_geom.bounds}), Source CRS: {aoi_src_crs}")
        except Exception as e:
            logger.error(f"Failed to parse AOI WKT: {e}", exc_info=True)
            aoi_geom = None

    # Parse datetime filter (same logic as before)
    datetime_filter: Optional[Tuple[str, str]] = None
    if load_params.datetime_filter:
        try:
            start_str, end_str = load_params.datetime_filter.split('/')
            if start_str and end_str:
                 datetime_filter = (start_str, end_str)
                 logger.info(f"Using datetime filter: {datetime_filter}")
            else:
                 logger.warning(f"Invalid datetime filter format ignored: {load_params.datetime_filter}")
        except ValueError:
            logger.warning(f"Invalid datetime filter format ignored: {load_params.datetime_filter}")

    # Create band aliases mapping and collect all possible aliases for catalog query
    band_aliases = {}
    catalog_band_queries = set()
    
    # First, expand all band names to their possible aliases
    for band_name in load_params.bands:
        # Get all possible aliases for this band
        aliases = get_band_aliases(load_params.collection, band_name)
        # Map each alias back to the original band name
        for alias in aliases:
            band_aliases[alias] = band_name
        # Add all aliases to the catalog queries
        catalog_band_queries.update(aliases)
    
    # Also include the original band names in the queries
    catalog_band_queries.update(load_params.bands)
    
    logger.info(f"Created band aliases mapping: {band_aliases}")
    logger.info(f"Using these bands for catalog query: {catalog_band_queries}")

    # Build catalog query parameters
    catalog_query_params = {
        'collection': load_params.collection,
        'aoi': aoi_geom,  # Catalog client handles spatial filtering
        'datetime_range': datetime_filter,
        'bands': list(catalog_band_queries) or None,  # Use all aliases for query
        'property_filters': None,
        'limit': None  # Apply limits after generating specs
    }
    
    logger.debug(f"Catalog query params: {catalog_query_params}")

    try:
        matching_assets_table = await catalog_client.query_assets(**catalog_query_params)

        if not isinstance(matching_assets_table, pa.Table) or matching_assets_table.num_rows == 0:
            logger.warning("Catalog query returned zero matching assets or failed.")
            return # Terminate generator

        logger.info(f"Catalog query successful. Found {matching_assets_table.num_rows} candidate assets.")

    except Exception as e:
        logger.error(f"Catalog query failed: {e}", exc_info=True)
        return # Terminate generator

    # --- 3. Determine Required Bands ---
    # Determine the full set of bands needed based on load_step and apply_steps
    explicit_load_bands = set(load_params.bands)
    inferred_apply_bands = set()
    apply_step_func_ids = [step.function_id for step in plan.apply_steps]
    
    logger.info(f"Explicitly loading bands: {explicit_load_bands}")
    logger.info(f"Apply step function IDs: {apply_step_func_ids}")

    # Use the canonical operation schemas to infer required bands
    try:
        from tfw_raster_schemas.operations import get_required_bands_for_operation, OPERATIONS_SCHEMA
        
        # Helper function to extract the core function ID from a namespaced ID
        def get_core_function_id(namespaced_id):
            # If the ID has a namespace (like terrafloww.spectral.ndvi), extract the last part
            if "." in namespaced_id:
                return namespaced_id.split(".")[-1]
            return namespaced_id
        
        # For each apply step, get the required bands from the schema
        for func_id in apply_step_func_ids:
            # Try with both namespaced and core function ID
            core_func_id = get_core_function_id(func_id)
            logger.info(f"Processing function: {func_id} (core: {core_func_id})")
            
            # First try with the full function ID, then with the core ID
            required_bands = get_required_bands_for_operation(func_id)
            if not required_bands:
                required_bands = get_required_bands_for_operation(core_func_id)
            
            if required_bands:
                logger.info(f"  Required bands from schema: {required_bands}")
                # Handle placeholder band names like <band_a> by looking at parameters
                if any(band.startswith('<') and band.endswith('>') for band in required_bands):
                    # Find the corresponding apply step to get parameters
                    for step in plan.apply_steps:
                        if step.function_id == func_id:
                            params = {k: v for k, v in step.parameters.items()}
                            logger.info(f"  Step parameters: {params}")
                            # Replace placeholders with actual band names from parameters
                            for band in list(required_bands):
                                if band.startswith('<') and band.endswith('>'):
                                    param_name = band[1:-1]  # Remove < and >
                                    if param_name in params:
                                        param_value = params[param_name]
                                        required_bands.remove(band)
                                        # If the parameter is a list, extend with all values
                                        if isinstance(param_value, list):
                                            required_bands.extend(param_value)
                                        else:
                                            required_bands.append(param_value)
                                        logger.info(f"  Resolved placeholder {band} to {param_value}")
                
                # Add all required bands to the set
                inferred_apply_bands.update(required_bands)
                logger.info(f"  Updated inferred bands: {inferred_apply_bands}")
            else:
                # Fallback for operations not in the schema
                logger.info(f"  No schema found, using hardcoded fallback for {core_func_id}")
                if core_func_id == "ndvi":
                    # For NDVI, we need both red and nir bands
                    ndvi_bands = ["red", "nir"]
                    inferred_apply_bands.update(ndvi_bands)
                    logger.info(f"  Added NDVI bands: {ndvi_bands}")
                elif core_func_id == "evi":
                    evi_bands = ["red", "nir", "blue"]
                    inferred_apply_bands.update(evi_bands)
                    logger.info(f"  Added EVI bands: {evi_bands}")
                elif core_func_id == "ndwi":
                    ndwi_bands = ["green", "nir"]
                    inferred_apply_bands.update(ndwi_bands)
                    logger.info(f"  Added NDWI bands: {ndwi_bands}")
    except ImportError:
        # Fallback if tfw_raster_schemas is not available
        logger.warning("tfw_raster_schemas.operations not available, using hardcoded band inference")
        if "ndvi" in apply_step_func_ids:
            ndvi_bands = ["red", "nir"]
            inferred_apply_bands.update(ndvi_bands)
            logger.info(f"Added NDVI bands (fallback): {ndvi_bands}")
        if "evi" in apply_step_func_ids:
            evi_bands = ["red", "nir", "blue"]
            inferred_apply_bands.update(evi_bands)
            logger.info(f"Added EVI bands (fallback): {evi_bands}")
        if "ndwi" in apply_step_func_ids:
            ndwi_bands = ["green", "nir"]
            inferred_apply_bands.update(ndwi_bands)
            logger.info(f"Added NDWI bands (fallback): {ndwi_bands}")

    required_bands = explicit_load_bands.union(inferred_apply_bands)
    if not required_bands:
        # If no bands specified in load and none inferred, what to do?
        # Option 1: Load all available bands (could be many)
        # Option 2: Error out
        # Option 3: Assume a default set (e.g., RGB)
        # Let's log a warning and attempt to load all unique bands found in the query results.
        all_bands_in_query = matching_assets_table['cog_key'].unique().to_pylist()
        if not all_bands_in_query:
            logger.error("No bands requested and no assets found to infer bands from.")
            return
        logger.warning(f"No specific bands requested or inferred. Planning for all found bands: {all_bands_in_query}")
        required_bands = set(all_bands_in_query)
    else:
        logger.info(f"Determined required bands for processing: {required_bands}")


    # --- 4. Filter Assets Table for Required Bands ---
    if required_bands:
        original_count = matching_assets_table.num_rows
        band_column = matching_assets_table['cog_key']
        # Ensure required_bands list is not empty before filtering
        if required_bands:
            filter_mask = pc.is_in(band_column, pa.array(list(required_bands)))
            matching_assets_table = matching_assets_table.filter(filter_mask)

            if matching_assets_table.num_rows == 0:
                logger.warning(f"Filtering for required bands {required_bands} resulted in zero assets.")
                return # Terminate generator
            logger.info(f"Filtered assets for required bands: {original_count} -> {matching_assets_table.num_rows}")
        else:
             logger.warning("Required bands set is empty, cannot filter assets by band.")
             # Depending on policy, maybe return here? For now, proceed with all assets found initially.
             pass


    # --- 5. Apply Scene Limit (if any) ---
    # Group by scene_id first to apply limit correctly across bands
    # This step ensures we don't process more scenes than requested,
    # even if they have multiple required bands.
    if load_params.scene_limit > 0:
        original_asset_count = matching_assets_table.num_rows
        # Get unique scenes, sorted by datetime (use min datetime if multiple bands)
        # Aggregate to get the min datetime per scene
        scene_datetimes = matching_assets_table.group_by("scene_id").aggregate([("datetime", "min")])
        # Sort scenes (e.g., oldest first)
        scene_datetimes = scene_datetimes.sort_by([("datetime_min", "ascending")])

        if scene_datetimes.num_rows > load_params.scene_limit:
            # Select the scene_ids to keep
            keep_scene_ids_table = scene_datetimes.slice(0, load_params.scene_limit)
            keep_scene_ids = keep_scene_ids_table["scene_id"]
            logger.info(f"Limiting to {load_params.scene_limit} scenes: {keep_scene_ids.to_pylist()}")

            # Filter the main asset table
            filter_mask = pc.is_in(matching_assets_table["scene_id"], keep_scene_ids)
            matching_assets_table = matching_assets_table.filter(filter_mask)
            logger.info(f"Applied scene limit ({load_params.scene_limit}). Kept {matching_assets_table.num_rows} assets from {len(keep_scene_ids)} scenes (originally {original_asset_count} assets).")
        else:
            logger.info(f"Scene count ({scene_datetimes.num_rows}) is within limit ({load_params.scene_limit}). No scene limit applied.")

    # --- 6. Generate WindowSpec for each Asset/Band/Tile (Yielding) ---
    logger.info("Generating and yielding WindowSpec definitions...")
    assets_pylist = matching_assets_table.to_pylist()

    # Initialize data structures for window processing
    scene_windows = {}  # Dictionary to store window data keyed by (scene_id, tile_r, tile_c)
    unique_windows = set()  # Store (scene_id, tile_r, tile_c) tuples
    spatial_window_limit = plan.head_limit or float('inf')  # Use head_limit if set, else no limit
    windows_processed = 0
    required_bands = set(load_params.bands)  # Ensure required_bands is defined

    # Pre-group assets by scene_id for better iteration logic
    assets_by_scene = {}
    for asset in assets_pylist:
        if asset['scene_id'] not in assets_by_scene:
            assets_by_scene[asset['scene_id']] = []
        assets_by_scene[asset['scene_id']].append(asset)

    # Process scenes in order
    for scene_id, scene_assets in assets_by_scene.items():
        # Process each band in this scene
        for asset_meta in scene_assets:
            band_name = asset_meta.get('cog_key')
            scene_id = asset_meta.get('scene_id')

            # Basic check for essential fields
            missing_keys = [key for key in REQUIRED_CATALOG_FIELDS if key not in asset_meta or asset_meta[key] is None]
            missing_keys = [k for k in missing_keys if k not in ['cog_scale', 'cog_offset']]
            if missing_keys:
                logger.warning(f"Skipping asset row {scene_id}-{band_name} due to missing required catalog fields: {missing_keys}")
                continue

            # Validate transform format
            transform_input = asset_meta.get("cog_transform")
            if not isinstance(transform_input, (list, tuple)) or len(transform_input) != 6:
                logger.warning(f"Skipping asset row {scene_id}-{band_name} due to invalid transform format: {transform_input}")
                continue
            try:
                cog_transform_affine = Affine(*transform_input)
                cog_transform_elements = tuple(cog_transform_affine[:6])
            except Exception as aff_err:
                logger.warning(f"Skipping asset row {scene_id}-{band_name} due to transform error: {aff_err}")
                continue

            # Prepare grid definition
            try:
                grid_def = {
                    "transform": cog_transform_affine,
                    "width": int(asset_meta['cog_width']),
                    "height": int(asset_meta['cog_height']),
                    "tile_width": int(asset_meta['cog_tile_width']),
                    "tile_height": int(asset_meta['cog_tile_height']),
                }
            except (TypeError, ValueError) as grid_err:
                logger.warning(f"Skipping asset row {scene_id}-{band_name} due to invalid grid definition parameters: {grid_err}")
                continue

            # Reproject AOI if necessary
            aoi_for_asset = aoi_geom
            target_crs = asset_meta.get('cog_crs')
            if aoi_geom and aoi_src_crs and aoi_src_crs != target_crs:
                try:
                    logger.debug(f"Reprojecting AOI from {aoi_src_crs} to {target_crs} for {scene_id}-{band_name}")
                    aoi_for_asset = engine_utils._reproject_geometry(
                        aoi_geom, aoi_src_crs, target_crs
                    )
                except Exception as reproj_err:
                    logger.error(f"Failed to reproject AOI for asset {scene_id}-{band_name}: {reproj_err}. Skipping asset row.")
                    continue

            # Get intersecting tiles/windows for this asset
            try:
                intersecting_tiles = grid.get_intersecting_tiles_and_windows(
                    grid_definition=grid_def,
                    aoi=aoi_for_asset
                )
            except Exception as grid_intersect_err:
                logger.error(f"Failed to get intersecting tiles for {scene_id}-{band_name}: {grid_intersect_err}. Skipping asset row.")
                continue

            # Ensure tile offsets/counts are lists
            tile_offsets = asset_meta.get('cog_tile_offsets')
            tile_byte_counts = asset_meta.get('cog_tile_byte_counts')
            if not isinstance(tile_offsets, list) or not isinstance(tile_byte_counts, list):
                logger.warning(f"Asset {scene_id}-{band_name} has invalid tile offsets/counts type. Skipping.")
                continue

            tiles_across = (grid_def['width'] + grid_def['tile_width'] - 1) // grid_def['tile_width']

            # Format datetime
            dt_obj = asset_meta.get('datetime')
            datetime_iso_str: Optional[str] = None
            if dt_obj:
                try:
                    if not dt_obj.tzinfo: dt_obj = pytz.UTC.localize(dt_obj)
                    datetime_iso_str = dt_obj.isoformat()
                except Exception as dt_err:
                    logger.warning(f"Failed to format datetime {dt_obj} for {scene_id}-{band_name}: {dt_err}.")
                    datetime_iso_str = None

            # Get scale/offset with defaults
            scale_factor = float(asset_meta.get('cog_scale', 1.0) or 1.0)
            offset_factor = float(asset_meta.get('cog_offset', 0.0) or 0.0)            

            # Yield WindowSpec for each intersecting tile/window for THIS band
            for tile_info in intersecting_tiles:
                tile_r = tile_info['tile_r']
                tile_c = tile_info['tile_c']
                window = tile_info['window']

                # Create unique window identifier
                window_id = (scene_id, tile_r, tile_c)
                
                # Initialize window data if it doesn't exist
                if window_id not in scene_windows:
                    scene_windows[window_id] = {
                        'scene_id': scene_id,
                        'tile_r': tile_r,
                        'tile_c': tile_c,
                        'window': window,
                        'crs': asset_meta['cog_crs'],
                        'bands': {}
                    }
                
                # Add this band to the window
                scene_windows[window_id]['bands'][band_name] = {
                    'cog_href': asset_meta['cog_href'],
                    'catalog_band_name': band_name,
                    'cog_crs': asset_meta['cog_crs'],
                    'cog_transform': asset_meta['cog_transform'],
                    'cog_width': asset_meta['cog_width'],
                    'cog_height': asset_meta['cog_height'],
                    'cog_tile_width': asset_meta['cog_tile_width'],
                    'cog_tile_height': asset_meta['cog_tile_height'],
                    'cog_dtype': asset_meta['cog_dtype'],
                    'cog_predictor': asset_meta['cog_predictor'],
                    'cog_tile_offsets': asset_meta['cog_tile_offsets'],
                    'cog_tile_byte_counts': asset_meta['cog_tile_byte_counts'],
                    'cog_scale': scale_factor,
                    'cog_offset': offset_factor,
                    'collection': load_params.collection,
                    'datetime_iso_str': datetime_iso_str
                }

    # Now yield all bands for each window that has all required bands
    windows_yielded = 0
    for window_key, window_data in scene_windows.items():

        if not required_bands.issubset(window_data['bands'].keys()):
            # Get collection from the first band (they should all be from the same collection)
            collection = next(iter(window_data['bands'].values()))['collection']
            available_band_names = set(window_data['bands'].keys())
            
            # Check if any of the aliases for required bands are in available bands
            has_all_bands = True
            for band in required_bands:
                aliases = get_band_aliases(collection, band)
                if not any(alias in available_band_names for alias in aliases):
                    logger.debug(f"Missing band '{band}' (or any of its aliases: {aliases}) in window {window_key}")
                    has_all_bands = False
                    break
            
            if not has_all_bands:
                continue

        # Create WindowSpec for each band in this window
        for band_name, band_data in window_data['bands'].items():
            try:
                # Calculate window parameters
                window = window_data['window']
                
                # Calculate tile index and byte information
                tile_idx = window_data['tile_r'] * tiles_across + window_data['tile_c']
                byte_offset = band_data['cog_tile_offsets'][tile_idx]
                byte_size = band_data['cog_tile_byte_counts'][tile_idx]
                
                # Create the WindowSpec
                window_spec = WindowSpec(
                    cog_href=band_data['cog_href'],
                    scene_id=window_data['scene_id'],
                    catalog_band_name=band_name,
                    operation_band_name=band_name,
                    collection=load_params.collection,
                    byte_offset=byte_offset,
                    byte_size=byte_size,
                    window_col_off=int(window.col_off),
                    window_row_off=int(window.row_off),
                    window_width=int(window.width),
                    window_height=int(window.height),
                    tile_r=window_data['tile_r'],
                    tile_c=window_data['tile_c'],
                    tile_shape_decode_h=band_data['cog_tile_height'],
                    tile_shape_decode_w=band_data['cog_tile_width'],
                    cog_crs=band_data['cog_crs'],
                    cog_transform_elements=band_data['cog_transform'],
                    dtype_str=band_data['cog_dtype'],
                    predictor=band_data['cog_predictor'],
                    scale_factor=float(band_data['cog_scale']),
                    offset_factor=float(band_data['cog_offset']),
                    datetime_utc_iso=band_data.get('datetime_iso_str')
                )
                
                yield window_spec
                
            except Exception as e:
                logger.error(
                    f"Error creating WindowSpec for {window_data['scene_id']}-{band_name}, "
                    f"tile ({window_data['tile_r']},{window_data['tile_c']}): {e}",
                    exc_info=True
                )
        
        windows_yielded += 1
        if windows_yielded >= spatial_window_limit:
            logger.info(f"Reached spatial window limit ({spatial_window_limit}). Stopping further processing.")
            return

        # Break out of scene loop if we've reached the limit
        if windows_processed >= spatial_window_limit:
            logger.info(f"Reached spatial window limit ({spatial_window_limit}). Stopping further processing.")
            break

    logger.info(f"Finished yielding WindowSpec objects. Processed {windows_processed} unique spatial windows.")