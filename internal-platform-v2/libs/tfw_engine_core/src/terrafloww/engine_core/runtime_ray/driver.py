import logging
import asyncio
import os, sys
import time
import pyarrow as pa
import pyarrow.flight as flight
import ray
from typing import List, Dict, Any, Optional, Set, DefaultDict
from collections import defaultdict
from dataclasses import asdict

# --- Flight Status Cache ---
# Global cache for flight status
LOCAL_FLIGHT_RESULT_CACHE: Dict[str, pa.Table] = {}
LOCAL_FLIGHT_STATUS_CACHE: Dict[str, Dict[str, Any]] = {}
_FLIGHT_LOCK = asyncio.Lock()

from terrafloww.engine_core.runtime_ray.common_types import WindowSpec
from terrafloww.engine_core.catalog_client import get_band_aliases
from terrafloww.processing_engine.v1 import processing_engine_pb2
from .planner import plan_execution # Async Generator
from .worker import process_batch_on_worker # Ray remote function
from .common_types import WindowSpec

logger = logging.getLogger(__name__)

class RayDriver:
    """
    Orchestrates the execution of a geospatial workflow using Ray tasks,
    consuming specs from the planner and driving the workers.
    """

    def __init__(self):
        """
        Initializes the RayDriver.
        """
        self.output_storage_path = os.environ.get("TFW_OUTPUT_STORAGE_PATH", "/tmp/terrafloww/results")
        os.makedirs(self.output_storage_path, exist_ok=True)
        logger.info(f"RayDriver initialized. Output path: {self.output_storage_path}")

    async def _ensure_ray_initialized(self, address: str):
        """Ensure Ray is initialized."""
        if not ray.is_initialized():
            logger.info(f"Initializing Ray connection to address: '{address}'...")
            ray.init(address=address, ignore_reinit_error=True)
            logger.info("Ray initialized.")
        else:
            logger.info(f"Ray already initialized.")

    async def execute_workflow(self, execution_id: str, plan: processing_engine_pb2.WorkflowPlan):
        """
        Asynchronously executes the workflow plan using Ray tasks.
        """
        start_time = time.time()
        logger.info(f"RayDriver starting execution for ID: {execution_id}")

        try:
            # --- 1. Planning & Grouping Phase ---
            # Group specs by their spatial window (scene_id, tile_r, tile_c)
            # and track which bands are available for each window
            spatial_windows = defaultdict(lambda: {
                'specs': [],
                'bands': set(),
                'operation_bands': set()
            })
            
            target_window_count = plan.head_limit if plan.head_limit > 0 else float('inf')
            spec_receive_count = 0
            processed_any_spec = False

            # Determine required bands from plan
            required_operation_bands = set()  # The band names used in the operation (e.g., 'red', 'nir')
            required_catalog_bands = set()    # All possible band aliases that could satisfy the requirements
            band_aliases = {}  # Map operation band names to their canonical catalog names
            
            # First process load step bands
            for band_name in plan.load_step.bands:
                aliases = get_band_aliases(plan.load_step.collection, band_name)
                if aliases:
                    band_aliases[band_name] = aliases[0]  # First alias is canonical
                    required_operation_bands.add(band_name)
                    required_catalog_bands.update(aliases)
            
            # Then process apply steps (e.g., NDVI)
            for step in plan.apply_steps:
                if step.function_id == "terrafloww.spectral.ndvi":
                    params = step.parameters
                    red_band = params.fields.get("red_band", {}).string_value or "red"
                    nir_band = params.fields.get("nir_band", {}).string_value or "nir"
                    
                    # Get all aliases for these bands
                    red_aliases = get_band_aliases(plan.load_step.collection, red_band)
                    nir_aliases = get_band_aliases(plan.load_step.collection, nir_band)
                    
                    if red_aliases and nir_aliases:
                        band_aliases[red_band] = red_aliases[0]
                        band_aliases[nir_band] = nir_aliases[0]
                        required_operation_bands.update([red_band, nir_band])
                        required_catalog_bands.update(red_aliases + nir_aliases)
                    else:
                        logger.warning(f"Could not resolve band aliases for red={red_band} or nir={nir_band}")

            logger.info(f"Required operation bands: {required_operation_bands}")
            logger.info(f"Required catalog bands: {required_catalog_bands}")
            logger.info(f"Band aliases mapping: {band_aliases}")
            
            # Now process the specs from the planner
            logger.info(f"Consuming specs from planner, head limit={target_window_count} windows...")
            
            # Get the planner generator
            planner_generator = plan_execution(plan)
            
            # Process specs from the planner
            async for spec in planner_generator:
                processed_any_spec = True
                spec_receive_count += 1
                
                # Create a spatial key (scene_id, tile_r, tile_c)
                spatial_key = (spec.scene_id, spec.tile_r, spec.tile_c)
                
                # Add the spec to its spatial window
                window = spatial_windows[spatial_key]
                window['specs'].append(spec)
                window['bands'].add(spec.catalog_band_name)
                if hasattr(spec, 'operation_band_name'):
                    op_band = spec.operation_band_name
                    window['operation_bands'].add(op_band)
                    logger.debug(f"Added {op_band} (band: {spec.catalog_band_name}) to window {spatial_key}")
                
                # Stop if we've reached our target number of windows with all required bands
                complete_windows = [
                    k for k, v in spatial_windows.items() 
                    if required_operation_bands.issubset(v['operation_bands'])
                ]
                
                if len(complete_windows) >= target_window_count:
                    logger.info(f"Reached target of {target_window_count} complete windows. Stopping planner.")
                    break

            if not processed_any_spec:
                logger.warning(f"Execution {execution_id}: Planner yielded zero specs.")
                return

            # Filter to only windows that have all required operation bands
            complete_windows = {
                k: v for k, v in spatial_windows.items()
                if required_operation_bands.issubset(v['operation_bands'])
            }
            
            logger.info(f"Planning phase complete. Found {len(complete_windows)} complete spatial windows with all required bands.")
            
            if not complete_windows:
                logger.warning(f"No complete spatial windows found with all required bands: {required_operation_bands}")
                return

            # --- 2. Execution Phase ---
            task_futures = []
            plan_apply_steps_serializable = [
                {"function_id": step.function_id, "parameters": dict(step.parameters)}
                for step in plan.apply_steps
            ]

            # Submit one Ray task per complete spatial window
            for spatial_key, window_data in list(complete_windows.items())[:target_window_count]:
                specs_for_chunk = window_data['specs']
                logger.debug(f"Submitting task for spatial window {spatial_key} with {len(specs_for_chunk)} band specs.")
                
                    # Get Flight server endpoint from environment or use default
                flight_endpoint = os.environ.get("FLIGHT_ENDPOINT", "grpc://localhost:50052")
                
                # Submit the task with Flight server info
                task_futures.append(
                    process_batch_on_worker.remote(
                        specs_for_chunk,
                        plan_apply_steps_serializable,
                        execution_id
                    )
                )
                logger.debug(f"Submitted task for window {spatial_key} with execution_id={execution_id}")

            logger.info(f"Submitted {len(task_futures)} Ray worker tasks.")

            # --- 3. Result Aggregation ---
            results_batches = []
            processed_row_count = 0

            # Blocking get - will raise RayTaskError if any worker fails
            logger.info(f"Waiting for {len(task_futures)} Ray task results...")
            try:
                results = ray.get(task_futures)
                logger.info(f"All {len(task_futures)} workers completed successfully")
            except ray.exceptions.RayTaskError as e:
                error_msg = f"Ray worker task failed: {str(e)}"
                logger.error(error_msg)
                await self._signal_failure_to_flight_server(execution_id, error_msg)
                raise RuntimeError(error_msg) from e

            # All workers succeeded - aggregate results
            for result in results:
                if result is not None:
                    results_batches.append(result)
                    processed_row_count += result.num_rows

            # Workers upload results directly to Flight server
            # Driver just tracks completion and signals when all workers are done
            if results_batches:
                total_rows = sum(batch.num_rows for batch in results_batches if batch is not None)
                logger.info(f"All workers completed successfully. Total rows processed: {total_rows}")

                # Signal completion to Flight server
                await self._signal_completion_to_flight_server(execution_id)

                return None  # Workers already uploaded data
            else:
                logger.warning("No valid results received from workers.")
                await self._signal_failure_to_flight_server(execution_id, "No results from workers")
                return None

        except Exception as e:
            logger.error(f"Execution {execution_id} failed: {e}", exc_info=True)
            await self._signal_failure_to_flight_server(execution_id, str(e))
            return None

    async def _signal_completion_to_flight_server(self, execution_id: str):
        """
        Signal to Flight server that all workers have completed uploading results.
        """
        try:
            # Get Flight server connection details from environment
            flight_host = os.environ.get("FLIGHT_HOST", "localhost")
            flight_port = int(os.environ.get("FLIGHT_PORT", "50052"))
            flight_uri = f"grpc+tcp://{flight_host}:{flight_port}"

            # Connect to Flight server
            client = flight.FlightClient(flight_uri)

            # Create a special completion signal descriptor
            completion_signal = f"{execution_id}_COMPLETE"
            descriptor = flight.FlightDescriptor.for_command(completion_signal.encode())

            # Send empty table as completion signal
            empty_table = pa.Table.from_arrays([], names=[])
            writer, _ = client.do_put(descriptor, empty_table.schema)
            writer.write_table(empty_table)
            writer.close()

            logger.info(f"Signaled completion to Flight server for execution {execution_id}")

        except Exception as e:
            logger.error(f"Failed to signal completion to Flight server: {e}", exc_info=True)
            # Fallback to local cache
            async with _FLIGHT_LOCK:
                LOCAL_FLIGHT_STATUS_CACHE[execution_id] = {"status": "COMPLETED"}

    async def _signal_failure_to_flight_server(self, execution_id: str, error_message: str):
        """
        Signal to Flight server that the workflow failed.
        """
        try:
            # Get Flight server connection details from environment
            flight_host = os.environ.get("FLIGHT_HOST", "localhost")
            flight_port = int(os.environ.get("FLIGHT_PORT", "50052"))
            flight_uri = f"grpc+tcp://{flight_host}:{flight_port}"

            # Connect to Flight server
            client = flight.FlightClient(flight_uri)

            # Create a special failure signal descriptor
            failure_signal = f"{execution_id}_FAILED"
            descriptor = flight.FlightDescriptor.for_command(failure_signal.encode())

            # Send empty table with error message in metadata
            empty_table = pa.Table.from_arrays([], names=[])
            schema_with_error = empty_table.schema.with_metadata({
                b"error_message": error_message.encode("utf-8")
            })
            writer, _ = client.do_put(descriptor, schema_with_error)
            writer.write_table(empty_table)
            writer.close()

            logger.info(f"Signaled failure to Flight server for execution {execution_id}: {error_message}")

        except Exception as e:
            logger.error(f"Failed to signal failure to Flight server: {e}", exc_info=True)
            # Fallback to local cache
            async with _FLIGHT_LOCK:
                LOCAL_FLIGHT_STATUS_CACHE[execution_id] = {"status": "FAILED", "error": error_message}

    async def _signal_failure_to_flight_server(self, execution_id: str, error_message: str):
        """
        Signal to Flight server that execution failed.
        """
        try:
            # Update local cache for failure
            async with _FLIGHT_LOCK:
                LOCAL_FLIGHT_STATUS_CACHE[execution_id] = {
                    "status": "FAILED",
                    "details": {"error": error_message}
                }
            logger.info(f"Marked execution {execution_id} as failed: {error_message}")

        except Exception as e:
            logger.error(f"Failed to signal failure to Flight server: {e}", exc_info=True)

# --- Entry point (remains the same, uses singleton driver) ---
_driver_instance = None
_driver_lock = asyncio.Lock()

async def get_driver_instance():
    """Gets a singleton RayDriver instance safely."""
    global _driver_instance
    async with _driver_lock:
        if _driver_instance is None:
            _driver_instance = RayDriver()
            await _driver_instance._ensure_ray_initialized("auto")
    return _driver_instance

async def run_workflow_entrypoint(execution_id: str, plan: processing_engine_pb2.WorkflowPlan):
    """
    Main entry point called by the gRPC service to start a workflow.
    Uses a singleton driver instance. Ensures driver init is awaited.
    """
    driver = await get_driver_instance()
    return await driver.execute_workflow(execution_id, plan)