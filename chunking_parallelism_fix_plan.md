# Spatial Chunking & Parallelism Fix Plan

## Current Issue Analysis

### Problem Statement
- **Test Expectation**: `test_ndvi.py` expects 2 rows from `.head(2)` call
- **Actual Result**: Only 1 row returned
- **Root Cause**: Spatial chunking logic is not generating enough unique spatial windows for proper parallelism

### Current Behavior
```python
# Test code that's failing:
collection = collection.head(2)
result = collection.compute()
assert result.num_rows == 2, "Expected exactly 2 chunks (2 windows with multi-band data + ndvi)"
```

### Platform Status ✅
- **Error Handling**: ✅ Fixed - Workers properly raise exceptions, driver catches failures
- **End-to-End Processing**: ✅ Working - Complete workflow with NDVI calculation
- **Cloud Storage**: ✅ Working - AWS S3 Delta Lake catalog operational
- **Ray Distributed Processing**: ✅ Working - Ray workers connect and process data

## Technical Analysis

### Current Chunking Logic (planner.py)
```python
# Current implementation in plan_execution():
spatial_window_limit = plan.head_limit or float('inf')
unique_windows = set()  # Store (scene_id, tile_r, tile_c) tuples
windows_processed = 0

# Issue: Only processing windows until limit reached, but not ensuring
# enough unique spatial windows are generated for parallelism
```

### Chunking Strategies for Maximum Parallelism

#### Option 1: Enhanced Spatial Chunking (Recommended)
- **Goal**: Generate multiple spatial chunks from each scene
- **Approach**: Subdivide large tiles into smaller spatial windows
- **Benefits**: Better parallelism for large AOI processing
- **Implementation**: Modify `get_intersecting_tiles_and_windows()` to create sub-windows

#### Option 2: Temporal Chunking
- **Goal**: Process chunks from multiple datetime scenes
- **Approach**: Ensure head(N) gets N chunks from different temporal windows
- **Benefits**: Temporal parallelism across different acquisition dates
- **Implementation**: Modify planner to prioritize temporal diversity

#### Option 3: Hybrid Approach (Best for Production)
- **Goal**: Combine spatial and temporal chunking
- **Approach**: Generate multiple spatial chunks per scene AND across multiple scenes
- **Benefits**: Maximum parallelism for both spatial and temporal dimensions

## Recommended Solution: Enhanced Spatial Chunking

### Implementation Plan

#### Step 1: Modify Grid Generation Logic
**File**: `libs/tfw_engine_core/src/terrafloww/engine_core/grid.py`

**Current**: `get_intersecting_tiles_and_windows()` returns one window per tile
**Target**: Return multiple sub-windows per tile for better spatial parallelism

```python
def get_intersecting_tiles_and_windows(
    grid_definition: Dict[str, Any],
    aoi: Optional[BaseGeometry] = None,
    spatial_subdivision_factor: int = 2,  # NEW: Split each tile into NxN sub-windows
    debug: bool = False
) -> List[Dict[str, Any]]:
```

#### Step 2: Update Planner Logic
**File**: `libs/tfw_engine_core/src/terrafloww/engine_core/runtime_ray/planner.py`

**Current**: Tracks unique windows as `(scene_id, tile_r, tile_c)`
**Target**: Track sub-windows as `(scene_id, tile_r, tile_c, sub_r, sub_c)`

#### Step 3: Ensure Head Limit Respects Spatial Diversity
**Current**: `_filter_unique_chunks()` in SDK just takes first N rows
**Target**: Ensure head(N) gets N spatially diverse chunks

### Expected Outcomes

#### For `.head(2)` Test Case:
- **Before**: 1 spatial chunk → 1 row
- **After**: 2 spatial chunks → 2 rows
- **Parallelism**: 2x Ray worker utilization

#### For Large AOI Processing:
- **Before**: Limited by number of tiles
- **After**: Configurable spatial subdivision for optimal parallelism
- **Scalability**: Better resource utilization across Ray cluster

## Implementation Steps

### Phase 1: Core Chunking Logic ⏳
1. [ ] Modify `get_intersecting_tiles_and_windows()` to support spatial subdivision
2. [ ] Update window tracking in planner to handle sub-windows
3. [ ] Ensure unique window identification includes sub-window coordinates

### Phase 2: SDK Integration ⏳
1. [ ] Update `_filter_unique_chunks()` to respect spatial diversity
2. [ ] Ensure head(N) limit works with new chunking logic
3. [ ] Maintain backward compatibility

### Phase 3: Testing & Validation ⏳
1. [ ] Fix `test_ndvi.py` to pass with 2 rows
2. [ ] Verify no regression in existing functionality
3. [ ] Test parallelism improvements with larger AOIs

### Phase 4: Performance Optimization ⏳
1. [ ] Add configurable subdivision factor
2. [ ] Optimize for different AOI sizes
3. [ ] Add metrics for parallelism effectiveness

## Risk Mitigation

### Backward Compatibility
- Default subdivision factor = 1 (no change in behavior)
- Existing tests should continue to pass
- Gradual rollout with feature flags

### Performance Considerations
- Monitor memory usage with increased chunk count
- Ensure Ray cluster can handle increased task parallelism
- Add configurable limits to prevent resource exhaustion

## Success Criteria

### Immediate (Phase 1-2)
- [ ] `test_ndvi.py` passes with `assert result.num_rows == 2`
- [ ] No regression in existing test suite
- [ ] Platform maintains end-to-end functionality

### Long-term (Phase 3-4)
- [ ] Improved parallelism for large AOI processing
- [ ] Configurable chunking strategies
- [ ] Production-ready scalability

## Next Actions

1. **Start with Phase 1**: Modify grid generation logic
2. **Test incrementally**: Ensure each change doesn't break existing functionality
3. **Document changes**: Update any affected documentation
4. **Monitor performance**: Track improvements in Ray cluster utilization
